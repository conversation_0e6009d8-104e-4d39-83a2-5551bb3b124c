/**
 * Legacy Google Drive utilities - maintained for backward compatibility
 * New implementations should use services/google-drive.service.js and services/folder-structure.service.js
 */

import { googleDriveService } from '@/services/google-drive.service.js';
import { folderStructureService } from '@/services/folder-structure.service.js';
import { generateUniqueFileName } from '@/utils/file-utils.js';
import { ENV } from '@/config/environment.js';

/**
 * Legacy function - use googleDriveService.initialize() instead
 */

export async function getDriveClient() {
  return await googleDriveService.initialize();
}

/**
 * Legacy function - use folderStructureService.getGlobalFolderStructure() instead
 */

async function getGlobalFolderStructure() {
  return await folderStructureService.getGlobalFolderStructure();
}

/**
 * Legacy function - use googleDriveService.findOrCreateFolder() instead
 */
async function findOrCreateFolder(drive, folderName, parentId = null, shareWithEmail = false) {
  return await googleDriveService.findOrCreateFolder(folderName, parentId);
}

export async function createTeamFolderStructure(drive, teamData, customFolderNames = null) {
  return await folderStructureService.createTeamFolderStructure(teamData, customFolderNames);
}

export async function checkFolderExists(drive, teamData) {
  try {
    // Check if team has folder structure
    if (teamData.folderStructure && teamData.folderStructure.teamFolderId) {
      return {
        exists: true,
        structure: teamData.folderStructure,
        teamFolderId: teamData.folderStructure.teamFolderId
      };
    }

    return { exists: false, structure: null };
  } catch (error) {
    console.error('Error checking folder existence:', error);
    return { exists: false, structure: null };
  }
}

export async function ensureTeamFolderStructure(drive, teamData) {
  return await folderStructureService.ensureTeamFolderStructure(teamData);
}

export async function uploadToTeamFolder(drive, teamData, fileData, submissionType, subCategory = null, memberIndex = null) {
  try {
    console.log(`Starting upload for team: ${teamData.teamID}, type: ${submissionType}`);
    console.log(`Member index: ${memberIndex}, Team members count: ${teamData.members?.length || 0}`);

    // Validate file input
    if (!fileData || !fileData.buffer || !fileData.originalname) {
      throw new Error(`Invalid file data: Missing buffer or originalname`);
    }

    // Ensure folder structure exists
    if (!teamData.folderStructure || !teamData.folderStructure.memberFolders) {
      console.log('No folder structure found or missing member folders, creating proper structure');
      teamData.folderStructure = await folderStructureService.ensureTeamFolderStructure(teamData);
    }

    const { folderStructure } = teamData;
    let targetFolderId;
    let folderPath;

    // Determine correct upload folder
    if (submissionType === 'conceptNote') {
      if (subCategory && folderStructure.conceptNoteSubfolders?.[subCategory]) {
        targetFolderId = folderStructure.conceptNoteSubfolders[subCategory];
        folderPath = `Concept_Note/${subCategory}`;
      } else {
        targetFolderId = folderStructure.conceptNoteFolderId;
        folderPath = 'Concept_Note';
      }
    } else if (submissionType === 'finalDeliverable') {
      targetFolderId = folderStructure.finalDeliverableFolderId;
      folderPath = 'Final_Deliverable';
    } else if (submissionType === 'certificate' && memberIndex !== null) {
      const memberFolder = folderStructure.memberFolders[memberIndex];
      if (!memberFolder) {
        throw new Error(`Member folder not found for index: ${memberIndex}`);
      }
      targetFolderId = memberFolder.certificateFolderId;
      folderPath = `Member_Submissions/${memberFolder.memberName || `Member_${memberIndex + 1}`}/Certificates`;
    } else if (submissionType === 'resume' && memberIndex !== null) {
      const memberFolder = folderStructure.memberFolders[memberIndex];
      if (!memberFolder) {
        throw new Error(`Member folder not found for index: ${memberIndex}`);
      }
      targetFolderId = memberFolder.resumeFolderId;
      folderPath = `Member_Submissions/${memberFolder.memberName || `Member_${memberIndex + 1}`}/Resume_LinkedIn`;
    } else {
      throw new Error(`Invalid submission type: ${submissionType} or missing member index: ${memberIndex}`);
    }

    if (!targetFolderId) {
      throw new Error(`Target folder ID not found for submission type: ${submissionType}`);
    }

    // Generate unique file name
    const fileName = generateUniqueFileName(fileData.originalname, submissionType);
    console.log(`Uploading file: ${fileName} to folder: ${folderPath} (ID: ${targetFolderId})`);

    // Upload file using Google Drive service
    const uploadResult = await googleDriveService.uploadFile(fileData, targetFolderId, fileName);

    // Return result with folder path
    const result = {
      ...uploadResult,
      folderPath: `${teamData.teamID}/${folderPath}`,
    };

    console.log(`File uploaded successfully:`, result);
    return result;

  } catch (error) {
    console.error('Upload error in uploadToTeamFolder:', error);
    throw new Error(`Upload failed: ${error.message}`);
  }
}


// Legacy function for backward compatibility
export async function uploadTeamFiles(drive, teamData, memberIndex, fileData, type) {
  return uploadToTeamFolder(drive, teamData, fileData, type, null, memberIndex);
}