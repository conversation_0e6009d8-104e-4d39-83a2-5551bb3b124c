/**
 * Database and API utility functions
 */

/**
 * Creates a standardized API response object
 * @param {boolean} success - Whether the operation was successful
 * @param {string} message - Response message
 * @param {any} data - Optional data payload
 * @returns {object} Standardized response object
 */
export function createApiResponse(success, message, data = null) {
  const response = {
    success,
    message,
    timestamp: new Date().toISOString(),
  };

  if (data !== null) {
    response.data = data;
  }

  return response;
}

/**
 * Creates a standardized error response
 * @param {string} message - Error message
 * @param {string} code - Error code
 * @param {any} details - Optional error details
 * @returns {object} Standardized error response
 */
export function createErrorResponse(message, code = 'UNKNOWN_ERROR', details = null) {
  const response = {
    success: false,
    message,
    error: {
      code,
      timestamp: new Date().toISOString(),
    },
  };

  if (details !== null) {
    response.error.details = details;
  }

  return response;
}

/**
 * Validates required fields in an object
 * @param {object} obj - Object to validate
 * @param {string[]} requiredFields - Array of required field names
 * @returns {object} Validation result with success boolean and missing fields
 */
export function validateRequiredFields(obj, requiredFields) {
  const missing = requiredFields.filter(field => 
    obj[field] === undefined || obj[field] === null || obj[field] === ''
  );

  return {
    success: missing.length === 0,
    missing,
    message: missing.length > 0 ? `Missing required fields: ${missing.join(', ')}` : 'All required fields present'
  };
}

/**
 * Sanitizes a string for use in file names or folder names
 * @param {string} str - String to sanitize
 * @returns {string} Sanitized string
 */
export function sanitizeFileName(str) {
  if (typeof str !== 'string') return 'unnamed';
  return str
    .replace(/[^a-zA-Z0-9._-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '')
    .substring(0, 100); // Limit length
}

/**
 * Formats file size in human readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Checks if a file extension is allowed for a specific type
 * @param {string} fileName - File name
 * @param {string} type - File type (certificate, resume, etc.)
 * @returns {boolean} Whether the file type is allowed
 */
export function isAllowedFileType(fileName, type) {
  const allowedTypes = {
    certificate: ['.pdf', '.jpg', '.jpeg', '.png'],
    resume: ['.pdf', '.doc', '.docx'],
    conceptNote: ['.pdf', '.doc', '.docx'],
    finalDeliverable: ['.pdf', '.doc', '.docx', '.zip', '.rar'],
  };

  if (!allowedTypes[type]) return false;

  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return allowedTypes[type].includes(extension);
}

/**
 * Generates a unique file name with timestamp
 * @param {string} originalName - Original file name
 * @param {string} prefix - Prefix for the file name
 * @returns {string} Unique file name
 */
export function generateUniqueFileName(originalName, prefix = '') {
  const timestamp = Date.now();
  const extension = originalName.substring(originalName.lastIndexOf('.'));
  const baseName = sanitizeFileName(originalName.substring(0, originalName.lastIndexOf('.')));
  
  return `${prefix}${prefix ? '_' : ''}${timestamp}_${baseName}${extension}`;
}
