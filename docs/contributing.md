# Contributing Guide

Thank you for your interest in contributing to the PBL Dashboard! This guide will help you get started.

## 🤝 How to Contribute

### Reporting Issues

1. **Check existing issues** first to avoid duplicates
2. **Use the issue template** when creating new issues
3. **Provide detailed information** including:
   - Steps to reproduce
   - Expected vs actual behavior
   - Environment details
   - Screenshots if applicable

### Suggesting Features

1. **Open a feature request** issue
2. **Describe the use case** and benefits
3. **Provide mockups or examples** if possible
4. **Discuss implementation** approach

### Code Contributions

1. **Fork the repository**
2. **Create a feature branch** from `main`
3. **Make your changes** following our guidelines
4. **Test your changes** thoroughly
5. **Submit a pull request**

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- MongoDB (local or Atlas)
- Google Cloud Project with Drive API
- Git

### Setup Steps

1. **Clone your fork**
   ```bash
   git clone https://github.com/your-username/PBL_Dashboard.git
   cd PBL_Dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment**
   ```bash
   npm run setup
   # Or manually copy .env.example to .env.local and configure
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 📝 Coding Standards

### Code Style

- Use **ESLint** and **Prettier** for consistent formatting
- Follow **Next.js** best practices
- Use **TypeScript** for type safety
- Write **meaningful variable and function names**

### File Organization

```
├── app/                    # Next.js app directory
├── components/            # Reusable React components
├── services/             # Business logic services
├── utils/                # Utility functions
├── types/                # TypeScript definitions
├── config/               # Configuration files
└── docs/                 # Documentation
```

### Naming Conventions

- **Files**: kebab-case (`user-profile.jsx`)
- **Components**: PascalCase (`UserProfile`)
- **Functions**: camelCase (`getUserData`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_FILE_SIZE`)
- **Types**: PascalCase (`UserData`)

### Code Examples

**Good:**
```javascript
// services/user.service.js
export class UserService {
  async getUserById(id) {
    try {
      const user = await User.findById(id);
      if (!user) {
        throw new NotFoundError('User not found');
      }
      return user;
    } catch (error) {
      logger.error('Error fetching user', { id, error: error.message });
      throw error;
    }
  }
}
```

**Bad:**
```javascript
// utils/stuff.js
export function getUser(id) {
  const user = User.findById(id);
  return user;
}
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Writing Tests

- Write **unit tests** for utility functions
- Write **integration tests** for API endpoints
- Write **component tests** for React components
- Use **descriptive test names**

**Example:**
```javascript
// utils/__tests__/file-utils.test.js
describe('validateFileSize', () => {
  it('should return true for files within size limit', () => {
    expect(validateFileSize(1000, 2000)).toBe(true);
  });

  it('should return false for files exceeding size limit', () => {
    expect(validateFileSize(3000, 2000)).toBe(false);
  });
});
```

## 📚 Documentation

### Code Documentation

- Use **JSDoc** for function documentation
- Include **parameter types** and **return types**
- Provide **usage examples** for complex functions

**Example:**
```javascript
/**
 * Uploads a file to Google Drive
 * @param {Object} fileData - File data object
 * @param {string} targetFolderId - Target folder ID
 * @param {string} fileName - File name
 * @returns {Promise<Object>} Upload result with file ID and links
 * @throws {ExternalServiceError} When Google Drive API fails
 */
async uploadFile(fileData, targetFolderId, fileName) {
  // Implementation
}
```

### README Updates

- Update README for **new features**
- Add **configuration options**
- Include **usage examples**

## 🔄 Pull Request Process

### Before Submitting

1. **Ensure tests pass**
   ```bash
   npm test
   npm run lint
   ```

2. **Update documentation** if needed

3. **Test your changes** manually

4. **Rebase your branch** on latest main
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

### PR Guidelines

1. **Use descriptive titles**
   - ✅ "Add file validation for team uploads"
   - ❌ "Fix bug"

2. **Provide detailed description**
   - What changes were made
   - Why the changes were necessary
   - How to test the changes

3. **Link related issues**
   - "Fixes #123"
   - "Closes #456"

4. **Keep PRs focused**
   - One feature/fix per PR
   - Avoid mixing unrelated changes

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Manual testing completed
- [ ] New tests added (if applicable)

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

## 🐛 Bug Fixes

### Debugging Process

1. **Reproduce the issue** locally
2. **Identify the root cause**
3. **Write a test** that fails
4. **Fix the issue**
5. **Verify the test passes**
6. **Test edge cases**

### Common Issues

- **Google Drive API errors**: Check credentials and permissions
- **Database connection**: Verify MongoDB URI and network
- **File upload failures**: Check file size and type validation
- **Authentication issues**: Verify JWT configuration

## 🚀 Feature Development

### Planning

1. **Discuss the feature** in an issue first
2. **Break down into smaller tasks**
3. **Consider backward compatibility**
4. **Plan database migrations** if needed

### Implementation

1. **Start with types/interfaces**
2. **Implement core logic in services**
3. **Add API endpoints**
4. **Create UI components**
5. **Add tests and documentation**

### Example Feature: Email Notifications

```
1. Define email types and templates
2. Create EmailService class
3. Add email configuration
4. Integrate with existing workflows
5. Add admin controls
6. Write tests
7. Update documentation
```

## 📋 Code Review

### As a Reviewer

- **Be constructive** and helpful
- **Explain the "why"** behind suggestions
- **Test the changes** locally if possible
- **Check for edge cases**
- **Verify documentation** is updated

### As an Author

- **Respond to feedback** promptly
- **Ask questions** if unclear
- **Make requested changes** or explain why not
- **Thank reviewers** for their time

## 🎯 Best Practices

### Security

- **Never commit secrets** or credentials
- **Validate all inputs** on server side
- **Use parameterized queries** for database
- **Implement proper authentication**

### Performance

- **Optimize database queries**
- **Use appropriate caching**
- **Minimize bundle size**
- **Implement pagination** for large datasets

### Accessibility

- **Use semantic HTML**
- **Provide alt text** for images
- **Ensure keyboard navigation**
- **Test with screen readers**

## 🆘 Getting Help

- **Check existing documentation** first
- **Search closed issues** for similar problems
- **Ask in discussions** for general questions
- **Create an issue** for bugs or feature requests
- **Join our community** chat (if available)

## 📄 License

By contributing, you agree that your contributions will be licensed under the same license as the project (MIT License).

Thank you for contributing to the PBL Dashboard! 🎉
