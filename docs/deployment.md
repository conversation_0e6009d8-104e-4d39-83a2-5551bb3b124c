# Deployment Guide

This guide covers deploying the PBL Dashboard to various platforms.

## 🚀 Vercel Deployment (Recommended)

Vercel is the recommended platform for deploying Next.js applications.

### Prerequisites

- Vercel account
- GitHub repository
- MongoDB Atlas database
- Google Service Account credentials

### Steps

1. **Prepare your repository**
   ```bash
   git add .
   git commit -m "Prepare for deployment"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Configure project settings

3. **Environment Variables**
   Add the following environment variables in Vercel dashboard:
   ```
   NODE_ENV=production
   NEXT_PUBLIC_BASE_URL=https://your-domain.vercel.app
   MONGODB_URI=mongodb+srv://username:<EMAIL>/database
   GOOGLE_SERVICE_ACCOUNT_PATH=/tmp/google-credentials.json
   ADMIN_EMAIL=<EMAIL>
   JWT_SECRET=your-production-jwt-secret
   JWT_EXPIRES_IN=24h
   MAX_FILE_SIZE=********
   ```

4. **Google Service Account**
   - Upload your `google-credentials.json` as a file in Vercel
   - Or encode it as base64 and decode in runtime

5. **Deploy**
   - Vercel will automatically deploy on push to main branch
   - Monitor deployment logs for any issues

## 🐳 Docker Deployment

### Dockerfile

Create a `Dockerfile` in the project root:

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose

Create a `docker-compose.yml`:

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/ibm-skillsbuild
      - JWT_SECRET=your-jwt-secret
    depends_on:
      - mongo
    volumes:
      - ./google-credentials.json:/app/google-credentials.json

  mongo:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=ibm-skillsbuild

volumes:
  mongo_data:
```

### Build and Run

```bash
# Build the image
docker build -t pbl-dashboard .

# Run with docker-compose
docker-compose up -d

# Or run standalone
docker run -p 3000:3000 \
  -e MONGODB_URI=your-mongodb-uri \
  -e JWT_SECRET=your-jwt-secret \
  -v $(pwd)/google-credentials.json:/app/google-credentials.json \
  pbl-dashboard
```

## ☁️ AWS Deployment

### Using AWS Amplify

1. **Connect Repository**
   - Go to AWS Amplify Console
   - Connect your GitHub repository

2. **Build Settings**
   Create `amplify.yml`:
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

3. **Environment Variables**
   Add environment variables in Amplify console

### Using EC2

1. **Launch EC2 Instance**
   - Choose Ubuntu 20.04 LTS
   - Configure security groups (ports 22, 80, 443, 3000)

2. **Install Dependencies**
   ```bash
   sudo apt update
   sudo apt install -y nodejs npm nginx
   sudo npm install -g pm2
   ```

3. **Deploy Application**
   ```bash
   git clone your-repository
   cd PBL_Dashboard
   npm install
   npm run build
   pm2 start npm --name "pbl-dashboard" -- start
   pm2 startup
   pm2 save
   ```

4. **Configure Nginx**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## 🌐 Other Platforms

### Netlify

1. Connect GitHub repository
2. Build command: `npm run build`
3. Publish directory: `.next`
4. Add environment variables
5. Configure redirects for SPA

### Railway

1. Connect GitHub repository
2. Add environment variables
3. Railway will auto-detect Next.js and deploy

### DigitalOcean App Platform

1. Create new app from GitHub
2. Configure build and run commands
3. Add environment variables
4. Deploy

## 🔧 Production Considerations

### Environment Variables

Ensure all production environment variables are set:
- Use strong JWT secrets
- Use production MongoDB URI
- Set proper CORS origins
- Configure proper logging levels

### Security

- Enable HTTPS
- Set up proper CORS policies
- Use environment-specific secrets
- Enable rate limiting
- Set up monitoring and alerting

### Performance

- Enable Next.js optimizations
- Use CDN for static assets
- Implement caching strategies
- Monitor performance metrics

### Monitoring

- Set up error tracking (Sentry)
- Monitor application performance
- Set up health checks
- Configure log aggregation

### Backup

- Regular database backups
- Google Drive folder backups
- Environment configuration backups

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check for TypeScript errors

2. **Database Connection**
   - Verify MongoDB URI format
   - Check network connectivity
   - Ensure database user permissions

3. **Google Drive API**
   - Verify service account credentials
   - Check API quotas and limits
   - Ensure proper folder permissions

4. **Environment Variables**
   - Verify all required variables are set
   - Check for typos in variable names
   - Ensure proper encoding for special characters

### Logs and Debugging

- Check application logs
- Monitor database connections
- Review Google Drive API usage
- Check network connectivity

For more help, refer to the main README or create an issue on GitHub.
