# API Documentation

This document describes the REST API endpoints available in the PBL Dashboard.

## Base URL

```
Development: http://localhost:3000/api
Production: https://your-domain.com/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": boolean,
  "message": string,
  "timestamp": string,
  "data": object | null,
  "error": {
    "code": string,
    "details": object | null
  } | null
}
```

## Team APIs

### Team Login

Authenticate a team and receive a JWT token.

**Endpoint:** `POST /api/team/login`

**Request Body:**
```json
{
  "teamID": "string",
  "email": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "jwt-token",
    "expiresIn": "24h",
    "team": {
      "teamID": "IBMSB2025...",
      "teamName": "Team Name",
      "email": "<EMAIL>"
    }
  }
}
```

### Get Team Details

Get details of the authenticated team.

**Endpoint:** `GET /api/team/[teamID]`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "Team details retrieved",
  "data": {
    "team": {
      "teamID": "string",
      "teamName": "string",
      "collegeName": "string",
      "leaderName": "string",
      "email": "string",
      "members": [
        {
          "fullName": "string",
          "email": "string",
          "phone": "string"
        }
      ],
      "folderStructure": {
        "teamFolderId": "string",
        "teamFolderLink": "string"
      }
    }
  }
}
```

### File Upload

Upload files for team submissions.

**Endpoint:** `POST /api/team/upload`

**Headers:** 
- `Authorization: Bearer <token>`
- `Content-Type: multipart/form-data`

**Form Data:**
- `file`: File to upload
- `type`: File type (`certificate`, `resume`, `conceptNote`, `finalDeliverable`)
- `teamID`: Team identifier
- `memberEmail`: Member email (for certificate/resume uploads)
- `subCategory`: Subcategory for concept notes (optional)

**Response:**
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "fileId": "string",
    "webViewLink": "string",
    "webContentLink": "string",
    "fileName": "string",
    "folderPath": "string"
  }
}
```

### Team Verification

Verify team token validity.

**Endpoint:** `POST /api/team/verify`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "Token is valid",
  "data": {
    "teamID": "string",
    "email": "string"
  }
}
```

## Admin APIs

### Admin Login

Authenticate an admin user.

**Endpoint:** `POST /api/admin/login`

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Admin login successful",
  "data": {
    "token": "jwt-token",
    "expiresIn": "24h",
    "admin": {
      "username": "string",
      "email": "string",
      "role": "admin"
    }
  }
}
```

### List Teams

Get a list of all teams.

**Endpoint:** `GET /api/admin/teams`

**Headers:** `Authorization: Bearer <admin-token>`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term (optional)

**Response:**
```json
{
  "success": true,
  "message": "Teams retrieved successfully",
  "data": {
    "teams": [
      {
        "teamID": "string",
        "teamName": "string",
        "collegeName": "string",
        "leaderName": "string",
        "email": "string",
        "membersCount": number,
        "folderStructureEnabled": boolean,
        "createdAt": "string"
      }
    ],
    "pagination": {
      "page": number,
      "limit": number,
      "total": number,
      "pages": number
    }
  }
}
```

### Get Team Details (Admin)

Get detailed information about a specific team.

**Endpoint:** `GET /api/admin/teams/[teamId]`

**Headers:** `Authorization: Bearer <admin-token>`

**Response:**
```json
{
  "success": true,
  "message": "Team details retrieved",
  "data": {
    "team": {
      "teamID": "string",
      "teamName": "string",
      "collegeName": "string",
      "leaderName": "string",
      "email": "string",
      "phone": "string",
      "members": [
        {
          "fullName": "string",
          "email": "string",
          "phone": "string"
        }
      ],
      "folderStructure": object,
      "folderStructureEnabled": boolean,
      "createdAt": "string",
      "updatedAt": "string"
    }
  }
}
```

### Bulk Team Upload

Upload multiple teams from CSV/Excel file.

**Endpoint:** `POST /api/admin/teams/upload`

**Headers:** 
- `Authorization: Bearer <admin-token>`
- `Content-Type: multipart/form-data`

**Form Data:**
- `file`: CSV or Excel file containing team data

**Response:**
```json
{
  "success": true,
  "message": "File processed successfully",
  "data": {
    "successful": number,
    "failed": number,
    "total": number,
    "errors": ["string"],
    "processedTeams": [
      {
        "teamID": "string",
        "teamName": "string",
        "leaderName": "string",
        "email": "string"
      }
    ]
  }
}
```

### Folder Management

Manage Google Drive folder structures for teams.

**Endpoint:** `POST /api/admin/folders`

**Headers:** `Authorization: Bearer <admin-token>`

**Request Body:**
```json
{
  "teamId": "string",
  "createStructure": boolean,
  "enableSubmissions": boolean,
  "disableSubmissions": boolean,
  "customFolderNames": {
    "rootFolder": "string",
    "conceptNoteFolder": "string",
    "finalDeliverableFolder": "string"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Folder structure created successfully",
  "data": {
    "folderStructure": {
      "teamFolderId": "string",
      "teamFolderLink": "string",
      "conceptNoteFolderId": "string",
      "finalDeliverableFolderId": "string",
      "memberFolders": object
    }
  }
}
```

### Get Folder Structure

Get folder structure information for teams.

**Endpoint:** `GET /api/admin/folders`

**Headers:** `Authorization: Bearer <admin-token>`

**Query Parameters:**
- `teamId`: Specific team ID (optional)

**Response:**
```json
{
  "success": true,
  "message": "Folder structure retrieved",
  "data": {
    "teams": [
      {
        "teamID": "string",
        "teamName": "string",
        "folderStructureEnabled": boolean,
        "folderStructure": object
      }
    ]
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AUTHENTICATION_ERROR` | Authentication required or failed |
| `AUTHORIZATION_ERROR` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `CONFLICT_ERROR` | Resource already exists |
| `RATE_LIMIT_ERROR` | Too many requests |
| `EXTERNAL_SERVICE_ERROR` | External service (Google Drive) error |
| `INTERNAL_ERROR` | Internal server error |

## Rate Limiting

API endpoints are rate limited to prevent abuse:
- 100 requests per 15 minutes per IP address
- Higher limits for authenticated requests
- File upload endpoints have stricter limits

## File Upload Limits

- Maximum file size: 10MB (configurable)
- Allowed file types:
  - Certificates: PDF, JPG, JPEG, PNG
  - Resumes: PDF, DOC, DOCX
  - Concept Notes: PDF, DOC, DOCX
  - Final Deliverables: PDF, DOC, DOCX, ZIP, RAR

## Examples

### JavaScript/Fetch

```javascript
// Team login
const response = await fetch('/api/team/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    teamID: 'IBMSB2025...',
    email: '<EMAIL>'
  })
});

const data = await response.json();
```

### cURL

```bash
# Team login
curl -X POST http://localhost:3000/api/team/login \
  -H "Content-Type: application/json" \
  -d '{"teamID":"IBMSB2025...","email":"<EMAIL>"}'

# File upload
curl -X POST http://localhost:3000/api/team/upload \
  -H "Authorization: Bearer <token>" \
  -F "file=@certificate.pdf" \
  -F "type=certificate" \
  -F "teamID=IBMSB2025..." \
  -F "memberEmail=<EMAIL>"
```

For more examples and detailed usage, refer to the main documentation.
