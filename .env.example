# Environment Configuration Example
# Copy this file to .env.local and fill in your actual values

# Node Environment
NODE_ENV=development

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:3000
PORT=3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/ibm-skillsbuild
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/database

# Google Drive API Configuration
# Path to your Google Service Account JSON file
GOOGLE_SERVICE_ACCOUNT_PATH=./google-credentials.json
# Admin email for Google Drive folder management
ADMIN_EMAIL=<EMAIL>

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# File Upload Configuration
MAX_FILE_SIZE=********
# Maximum file size in bytes (default: 10MB)

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
# Rate limit window in milliseconds (default: 15 minutes)
RATE_LIMIT_MAX_REQUESTS=100
# Maximum requests per window

# Logging Configuration
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=false

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN=*

# Google Drive Advanced Configuration
DRIVE_MAX_RETRIES=3
DRIVE_INITIAL_DELAY_MS=1000
DRIVE_BACKOFF_FACTOR=2
TEST_DRIVE_FOLDER_NAME=TestDrive

# Team Configuration
TEAM_MIN_MEMBERS=1
TEAM_MAX_MEMBERS=10
TEAM_ID_PREFIX=IBMSB2025

# Email Configuration (Optional - for future use)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=<EMAIL>

# Development Configuration
# Set to true to enable detailed error messages in development
NEXT_PUBLIC_SHOW_DETAILED_ERRORS=true

# Google Service Account Setup Instructions:
# 1. Go to Google Cloud Console (https://console.cloud.google.com/)
# 2. Create a new project or select existing one
# 3. Enable Google Drive API
# 4. Create a Service Account
# 5. Download the JSON key file
# 6. Place it in your project root and update GOOGLE_SERVICE_ACCOUNT_PATH
# 7. Share your Google Drive folders with the service account email

# MongoDB Setup Instructions:
# For local MongoDB:
# 1. Install MongoDB locally
# 2. Start MongoDB service
# 3. Use: mongodb://localhost:27017/ibm-skillsbuild
#
# For MongoDB Atlas:
# 1. Create account at https://www.mongodb.com/atlas
# 2. Create a cluster
# 3. Get connection string
# 4. Replace username, password, and database name
# 5. Use: mongodb+srv://username:<EMAIL>/database

# Security Notes:
# - Never commit .env files to version control
# - Use strong, unique JWT secrets in production
# - Regularly rotate API keys and secrets
# - Use environment-specific configurations
