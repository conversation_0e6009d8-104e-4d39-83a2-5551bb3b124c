#!/usr/bin/env node

/**
 * Setup script for PBL Dashboard
 * This script helps set up the development environment
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function createEnvFile() {
  console.log('\n🔧 Setting up environment configuration...\n');

  const envPath = path.join(process.cwd(), '.env.local');
  
  if (fs.existsSync(envPath)) {
    const overwrite = await question('⚠️  .env.local already exists. Overwrite? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') {
      console.log('Skipping environment setup.');
      return;
    }
  }

  console.log('Please provide the following configuration values:\n');

  const config = {};

  // Basic configuration
  config.NODE_ENV = await question('Environment (development/production) [development]: ') || 'development';
  config.NEXT_PUBLIC_BASE_URL = await question('Base URL [http://localhost:3000]: ') || 'http://localhost:3000';
  config.PORT = await question('Port [3000]: ') || '3000';

  // Database configuration
  console.log('\n📊 Database Configuration:');
  config.MONGODB_URI = await question('MongoDB URI [mongodb://localhost:27017/ibm-skillsbuild]: ') || 'mongodb://localhost:27017/ibm-skillsbuild';

  // Google Drive configuration
  console.log('\n☁️  Google Drive Configuration:');
  config.GOOGLE_SERVICE_ACCOUNT_PATH = await question('Google Service Account JSON path [./google-credentials.json]: ') || './google-credentials.json';
  config.ADMIN_EMAIL = await question('Admin email for Google Drive management: ');

  // Security configuration
  console.log('\n🔐 Security Configuration:');
  config.JWT_SECRET = await question('JWT Secret (leave empty to generate): ') || generateRandomSecret();
  config.JWT_EXPIRES_IN = await question('JWT Expires In [24h]: ') || '24h';

  // File upload configuration
  console.log('\n📁 File Upload Configuration:');
  config.MAX_FILE_SIZE = await question('Max file size in bytes [********]: ') || '********';

  // Generate .env.local content
  const envContent = Object.entries(config)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');

  fs.writeFileSync(envPath, envContent);
  console.log('\n✅ Environment configuration created successfully!');
  console.log(`📄 Configuration saved to: ${envPath}`);
}

function generateRandomSecret() {
  return require('crypto').randomBytes(32).toString('hex');
}

async function checkDependencies() {
  console.log('\n📦 Checking dependencies...\n');

  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ package.json not found. Please run this script from the project root.');
    process.exit(1);
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const requiredDeps = [
    'next',
    'react',
    'mongoose',
    'googleapis',
    'jsonwebtoken',
    'bcryptjs'
  ];

  const missingDeps = requiredDeps.filter(dep => 
    !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
  );

  if (missingDeps.length > 0) {
    console.log('❌ Missing required dependencies:');
    missingDeps.forEach(dep => console.log(`   - ${dep}`));
    console.log('\nPlease install missing dependencies and run setup again.');
    process.exit(1);
  }

  console.log('✅ All required dependencies are installed.');
}

async function checkGoogleCredentials() {
  console.log('\n☁️  Checking Google Drive setup...\n');

  const credentialsPath = path.join(process.cwd(), 'google-credentials.json');
  
  if (!fs.existsSync(credentialsPath)) {
    console.log('⚠️  Google Service Account credentials not found.');
    console.log('📋 To set up Google Drive integration:');
    console.log('   1. Go to Google Cloud Console (https://console.cloud.google.com/)');
    console.log('   2. Create a new project or select existing one');
    console.log('   3. Enable Google Drive API');
    console.log('   4. Create a Service Account');
    console.log('   5. Download the JSON key file');
    console.log('   6. Save it as "google-credentials.json" in the project root');
    console.log('   7. Share your Google Drive folders with the service account email');
    
    const skip = await question('\nSkip Google Drive setup for now? (Y/n): ');
    if (skip.toLowerCase() !== 'n') {
      console.log('⏭️  Skipping Google Drive setup. You can configure it later.');
      return;
    }
  } else {
    try {
      const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
      if (credentials.type === 'service_account') {
        console.log('✅ Google Service Account credentials found.');
        console.log(`📧 Service Account Email: ${credentials.client_email}`);
      } else {
        console.log('⚠️  Invalid Google Service Account credentials format.');
      }
    } catch (error) {
      console.log('❌ Error reading Google Service Account credentials:', error.message);
    }
  }
}

async function createDirectories() {
  console.log('\n📁 Creating required directories...\n');

  const directories = [
    'public/uploads',
    'logs',
    'temp'
  ];

  directories.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    } else {
      console.log(`📁 Directory already exists: ${dir}`);
    }
  });
}

async function main() {
  console.log('🚀 PBL Dashboard Setup\n');
  console.log('This script will help you set up the development environment.\n');

  try {
    await checkDependencies();
    await createEnvFile();
    await checkGoogleCredentials();
    await createDirectories();

    console.log('\n🎉 Setup completed successfully!\n');
    console.log('📋 Next steps:');
    console.log('   1. Review your .env.local file');
    console.log('   2. Set up Google Drive credentials if not done already');
    console.log('   3. Start MongoDB if using local instance');
    console.log('   4. Run "npm run dev" to start the development server');
    console.log('\n📚 For more information, check the README.md file.');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
