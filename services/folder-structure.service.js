/**
 * Folder Structure Service
 * Handles creation and management of team folder structures in Google Drive
 */

import { googleDriveService } from './google-drive.service.js';
import { sanitizeFileName } from '@/lib/db-utils';
import GlobalSettings from '@/models/GlobalSettings';
import connectDB from '@/lib/mongodb';

/**
 * Folder Structure Service Class
 */
export class FolderStructureService {
  constructor() {
    this.testDriveFolderId = null;
    this.defaultFolderStructure = {
      rootFolder: "IBM_SkillsBuild_Teams",
      conceptNoteFolder: "Concept_Note",
      finalDeliverableFolder: "Final_Deliverable",
      memberSubmissionsFolder: "Member_Submissions",
      certificatesFolder: "Certificates",
      resumeFolder: "Resume_LinkedIn",
      conceptNoteSubcategories: {
        problemStatement: "Problem_Statement",
        solutionApproach: "Solution_Approach",
        technicalArchitecture: "Technical_Architecture",
        implementationPlan: "Implementation_Plan",
        teamRoles: "Team_Roles"
      }
    };
  }

  /**
   * Get global folder structure settings from database
   */
  async getGlobalFolderStructure() {
    try {
      await connectDB();
      let settings = await GlobalSettings.findOne({ settingType: 'folderStructure' });
      
      if (!settings) {
        // Create default settings if none exist
        settings = new GlobalSettings({
          settingType: 'folderStructure',
          folderStructure: this.defaultFolderStructure
        });
        await settings.save();
      }
      
      return settings.folderStructure;
    } catch (error) {
      console.error('Error getting global folder structure:', error);
      return this.defaultFolderStructure;
    }
  }

  /**
   * Ensure TestDrive folder exists and is properly shared
   */
  async ensureTestDriveFolder() {
    if (this.testDriveFolderId) {
      return this.testDriveFolderId;
    }

    try {
      console.log('🔍 Looking for TestDrive folder...');
      
      // Initialize Google Drive service
      await googleDriveService.initialize();
      
      // Try to find existing TestDrive folder
      this.testDriveFolderId = await googleDriveService.findOrCreateFolder('TestDrive');
      
      // Check if TestDrive folder is properly shared
      const permissionCheck = await googleDriveService.checkFolderPermissions(this.testDriveFolderId);
      
      if (!permissionCheck.hasPublicAccess) {
        console.log('📤 Sharing TestDrive folder with everyone...');
        await googleDriveService.shareFolder(this.testDriveFolderId, {
          role: 'writer',
          type: 'anyone'
        });
        console.log('✅ TestDrive folder is now shared with everyone');
      } else {
        console.log('✅ TestDrive folder is already properly shared');
      }

      console.log(`📁 TestDrive folder ready with ID: ${this.testDriveFolderId}`);
      return this.testDriveFolderId;
    } catch (error) {
      console.error('❌ Error ensuring TestDrive folder:', error);
      throw error;
    }
  }

  /**
   * Create complete folder structure for a team
   */
  async createTeamFolderStructure(teamData, customFolderNames = null) {
    try {
      console.log(`🏗️ Creating folder structure for team: ${teamData.teamID}`);

      // Get folder structure settings
      const folderNames = customFolderNames || await this.getGlobalFolderStructure();

      // Ensure TestDrive folder exists
      const testDriveFolderId = await this.ensureTestDriveFolder();

      // Create main teams folder under TestDrive
      const teamsFolderId = await googleDriveService.findOrCreateFolder(
        folderNames.rootFolder, 
        testDriveFolderId
      );

      // Create team-specific folder
      const teamFolderName = `${teamData.teamID}_${sanitizeFileName(teamData.teamName || 'Team')}`;
      const teamFolderId = await googleDriveService.findOrCreateFolder(
        teamFolderName, 
        teamsFolderId
      );

      // Share team folder
      await googleDriveService.shareFolder(teamFolderId);

      // Generate team folder link
      const teamFolderLink = `https://drive.google.com/drive/folders/${teamFolderId}`;

      // Create main submission folders
      const conceptNoteFolderId = await googleDriveService.findOrCreateFolder(
        folderNames.conceptNoteFolder, 
        teamFolderId
      );
      
      const finalDeliverableFolderId = await googleDriveService.findOrCreateFolder(
        folderNames.finalDeliverableFolder, 
        teamFolderId
      );
      
      const membersSubmissionsFolderId = await googleDriveService.findOrCreateFolder(
        folderNames.memberSubmissionsFolder, 
        teamFolderId
      );

      // Create concept note subcategories
      const conceptNoteSubfolders = {};
      const subcategories = [
        { key: 'Problem_Statement', name: folderNames.conceptNoteSubcategories.problemStatement },
        { key: 'Solution_Approach', name: folderNames.conceptNoteSubcategories.solutionApproach },
        { key: 'Technical_Architecture', name: folderNames.conceptNoteSubcategories.technicalArchitecture },
        { key: 'Implementation_Plan', name: folderNames.conceptNoteSubcategories.implementationPlan },
        { key: 'Team_Roles', name: folderNames.conceptNoteSubcategories.teamRoles }
      ];

      for (const subcategory of subcategories) {
        const subfolderId = await googleDriveService.findOrCreateFolder(
          subcategory.name, 
          conceptNoteFolderId
        );
        conceptNoteSubfolders[subcategory.key] = subfolderId;
      }

      // Create member-specific folders
      const memberFolders = {};
      if (teamData.members && teamData.members.length > 0) {
        for (let i = 0; i < teamData.members.length; i++) {
          const member = teamData.members[i];
          const sanitizedName = sanitizeFileName(member.fullName || `Member_${i + 1}`);
          const memberFolderName = `Member_${i + 1}_${sanitizedName}`;
          
          const memberFolderId = await googleDriveService.findOrCreateFolder(
            memberFolderName, 
            membersSubmissionsFolderId
          );
          
          const certificateFolderId = await googleDriveService.findOrCreateFolder(
            folderNames.certificatesFolder, 
            memberFolderId
          );
          
          const resumeFolderId = await googleDriveService.findOrCreateFolder(
            folderNames.resumeFolder, 
            memberFolderId
          );
          
          memberFolders[i] = {
            folderId: memberFolderId,
            certificateFolderId,
            resumeFolderId,
            memberName: member.fullName,
            memberEmail: member.email
          };
        }
      }

      const folderStructure = {
        testDriveFolderId,
        teamsFolderId,
        teamFolderId,
        teamFolderLink,
        conceptNoteFolderId,
        conceptNoteSubfolders,
        finalDeliverableFolderId,
        membersSubmissionsFolderId,
        memberFolders,
        createdAt: new Date(),
        customFolderNames: folderNames
      };

      console.log(`✅ Folder structure created successfully for team: ${teamData.teamID}`);
      return folderStructure;
    } catch (error) {
      console.error('❌ Error creating folder structure:', error);
      throw error;
    }
  }

  /**
   * Create mock folder structure for development
   */
  async createMockFolderStructure(teamData) {
    try {
      console.log(`🧪 Creating mock folder structure for team: ${teamData.teamID}`);
      
      const folderNames = await this.getGlobalFolderStructure();
      
      const mockFolderStructure = {
        testDriveFolderId: `mock_testdrive_${Date.now()}`,
        teamsFolderId: `mock_teams_folder_${Date.now()}`,
        teamFolderId: `mock_team_${teamData.teamID}_${Date.now()}`,
        teamFolderLink: `https://drive.google.com/drive/folders/mock_team_${teamData.teamID}_${Date.now()}`,
        conceptNoteFolderId: `mock_concept_${teamData.teamID}_${Date.now()}`,
        conceptNoteSubfolders: {
          Problem_Statement: `mock_problem_${teamData.teamID}_${Date.now()}`,
          Solution_Approach: `mock_solution_${teamData.teamID}_${Date.now()}`,
          Technical_Architecture: `mock_tech_${teamData.teamID}_${Date.now()}`,
          Implementation_Plan: `mock_impl_${teamData.teamID}_${Date.now()}`,
          Team_Roles: `mock_roles_${teamData.teamID}_${Date.now()}`
        },
        finalDeliverableFolderId: `mock_final_${teamData.teamID}_${Date.now()}`,
        membersSubmissionsFolderId: `mock_members_${teamData.teamID}_${Date.now()}`,
        memberFolders: {},
        createdAt: new Date(),
        customFolderNames: folderNames
      };

      // Create member folders for all team members
      if (teamData.members && teamData.members.length > 0) {
        teamData.members.forEach((member, index) => {
          const memberName = sanitizeFileName(member.fullName || `Member_${index + 1}`);
          mockFolderStructure.memberFolders[index] = {
            folderId: `mock_member_${teamData.teamID}_${memberName}_${Date.now()}`,
            certificateFolderId: `mock_cert_${teamData.teamID}_${memberName}_${Date.now()}`,
            resumeFolderId: `mock_resume_${teamData.teamID}_${memberName}_${Date.now()}`,
            memberName: member.fullName,
            memberEmail: member.email
          };
        });
      }

      console.log(`✅ Mock folder structure created for ${teamData.members?.length || 0} members`);
      return mockFolderStructure;
    } catch (error) {
      console.error('❌ Error creating mock folder structure:', error);
      throw error;
    }
  }

  /**
   * Ensure team has proper folder structure
   */
  async ensureTeamFolderStructure(teamData) {
    try {
      // Check if we should use real or mock structure
      if (process.env.NODE_ENV === 'development' && !process.env.GOOGLE_SERVICE_ACCOUNT_PATH) {
        return await this.createMockFolderStructure(teamData);
      } else {
        return await this.createTeamFolderStructure(teamData);
      }
    } catch (error) {
      console.error('Error ensuring folder structure:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const folderStructureService = new FolderStructureService();
