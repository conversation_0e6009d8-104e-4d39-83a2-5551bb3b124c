/**
 * Google Drive Service
 * Handles all Google Drive API operations including folder creation and file uploads
 */

import { google } from 'googleapis';
import path from 'path';
import { Readable } from 'stream';
import { sanitizeFileName } from '@/lib/db-utils';
import logger from '@/utils/logger';
import { handleGoogleDriveError, ExternalServiceError } from '@/utils/error-handler';

const MAX_RETRIES = 3;
const INITIAL_DELAY_MS = 1000;
const BACKOFF_FACTOR = 2;

/**
 * Exponential backoff retry mechanism for API calls
 */
async function withExponentialBackoff(fn, retries = MAX_RETRIES, delay = INITIAL_DELAY_MS) {
  try {
    return await fn();
  } catch (error) {
    const shouldRetry = [403, 429, 500, 502, 503, 504].includes(error.code || error.status);
    if (retries > 0 && shouldRetry) {
      const nextDelay = delay * BACKOFF_FACTOR;
      logger.warn(`Retrying Google Drive operation in ${nextDelay}ms... (${retries} retries left)`, {
        error: error.message,
        code: error.code,
        retries
      });
      await new Promise(resolve => setTimeout(resolve, delay));
      return withExponentialBackoff(fn, retries - 1, nextDelay);
    }
    logger.error('Google Drive API error after retries', {
      error: error.message,
      code: error.code,
      status: error.status
    });
    handleGoogleDriveError(error);
  }
}

/**
 * Google Drive Service Class
 */
export class GoogleDriveService {
  constructor() {
    this.drive = null;
    this.adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  }

  /**
   * Initialize Google Drive client
   */
  async initialize() {
    if (this.drive) return this.drive;

    try {
      // Check if we're in development and use mock client
      if (process.env.NODE_ENV === 'development' && !process.env.GOOGLE_SERVICE_ACCOUNT_PATH) {
        console.log('Using mock Google Drive client for development');
        this.drive = this.createMockDriveClient();
        return this.drive;
      }

      if (!process.env.GOOGLE_SERVICE_ACCOUNT_PATH) {
        console.log('No Google Service Account path found, using mock client');
        this.drive = this.createMockDriveClient();
        return this.drive;
      }

      const auth = new google.auth.GoogleAuth({
        keyFile: path.resolve(process.env.GOOGLE_SERVICE_ACCOUNT_PATH),
        scopes: [
          'https://www.googleapis.com/auth/drive',
          'https://www.googleapis.com/auth/drive.file',
        ],
      });

      const authClient = await auth.getClient();
      this.drive = google.drive({ version: 'v3', auth: authClient });
      
      console.log('✅ Google Drive client initialized successfully');
      return this.drive;
    } catch (error) {
      console.error('Failed to initialize Google Drive client:', error);
      console.log('Falling back to mock Google Drive client');
      this.drive = this.createMockDriveClient();
      return this.drive;
    }
  }

  /**
   * Create mock Google Drive client for development
   */
  createMockDriveClient() {
    return {
      files: {
        list: async (params) => {
          console.log('Mock Drive: Listing files with query:', params?.q);
          return { 
            data: { 
              files: [] // Return empty to simulate no existing folders
            } 
          };
        },
        create: async (params) => {
          const isFolder = params.resource?.mimeType === 'application/vnd.google-apps.folder';
          const fileId = `mock_${isFolder ? 'folder' : 'file'}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          
          console.log(`Mock Drive: Creating ${isFolder ? 'folder' : 'file'} with name:`, params.resource?.name);
          
          return {
            data: {
              id: fileId,
              name: params.resource?.name,
              webViewLink: `https://drive.google.com/file/d/${fileId}/view`,
              webContentLink: `https://drive.google.com/uc?id=${fileId}`
            }
          };
        },
      },
      permissions: {
        list: async (params) => {
          console.log('Mock Drive: Listing permissions for file:', params.fileId);
          return {
            data: {
              permissions: [
                { type: 'user', role: 'owner' }
              ]
            }
          };
        },
        create: async (params) => {
          console.log('Mock Drive: Creating permission for file:', params.fileId);
          return { data: { id: 'mock_permission' } };
        }
      }
    };
  }

  /**
   * Find or create a folder in Google Drive
   */
  async findOrCreateFolder(folderName, parentId = null) {
    await this.initialize();
    
    return withExponentialBackoff(async () => {
      try {
        // Check if folder already exists
        let query = `mimeType = 'application/vnd.google-apps.folder' and name = '${folderName}' and trashed=false`;
        if (parentId) query += ` and '${parentId}' in parents`;

        console.log(`Searching for folder: ${folderName} with query: ${query}`);

        const folderSearch = await this.drive.files.list({
          q: query,
          fields: 'nextPageToken, files(id, name)',
          spaces: 'drive'
        });

        if (folderSearch.data.files.length) {
          // Folder already exists
          const folderId = folderSearch.data.files[0].id;
          console.log(`Found existing folder: ${folderName} with ID: ${folderId}`);
          return folderId;
        }

        // Folder does not exist so create
        console.log(`Creating new folder: ${folderName}`);

        const newFolder = await this.drive.files.create({
          resource: {
            name: folderName,
            mimeType: 'application/vnd.google-apps.folder',
            parents: parentId ? [parentId] : undefined
          },
          fields: 'id'
        });

        const folderId = newFolder.data.id;
        console.log(`Created folder: ${folderName} with ID: ${folderId}`);

        return folderId;
      } catch (error) {
        console.error(`Error in findOrCreateFolder for ${folderName}:`, error);
        throw error;
      }
    });
  }

  /**
   * Share a folder with specific permissions
   */
  async shareFolder(folderId, permissions = { role: 'writer', type: 'anyone' }) {
    await this.initialize();
    
    return withExponentialBackoff(async () => {
      try {
        // Share with admin email first
        if (this.adminEmail) {
          try {
            await this.drive.permissions.create({
              fileId: folderId,
              resource: {
                role: 'writer',
                type: 'user',
                emailAddress: this.adminEmail,
              },
              sendNotificationEmail: false,
            });
            console.log(`✅ Shared folder with ${this.adminEmail} as writer`);
          } catch (error) {
            console.warn(`⚠️ Failed to share folder with ${this.adminEmail}: ${error.message}`);
          }
        }

        // Then apply the requested permissions
        await this.drive.permissions.create({
          fileId: folderId,
          resource: permissions,
          sendNotificationEmail: false,
        });

        console.log(`✅ Applied permissions to folder: ${JSON.stringify(permissions)}`);
        return true;
      } catch (error) {
        console.warn('Failed to set folder permissions:', error.message);
        return false;
      }
    });
  }

  /**
   * Check if a folder is properly shared
   */
  async checkFolderPermissions(folderId) {
    await this.initialize();
    
    try {
      const permissions = await this.drive.permissions.list({
        fileId: folderId,
        fields: 'permissions(type, role)',
      });

      const hasPublicAccess = permissions.data.permissions.some(
        perm => perm.type === 'anyone' && (perm.role === 'reader' || perm.role === 'writer')
      );

      return {
        hasPublicAccess,
        permissions: permissions.data.permissions
      };
    } catch (error) {
      console.warn('Could not check folder permissions:', error.message);
      return { hasPublicAccess: false, permissions: [] };
    }
  }

  /**
   * Upload a file to Google Drive
   */
  async uploadFile(fileData, targetFolderId, fileName) {
    await this.initialize();
    
    return withExponentialBackoff(async () => {
      try {
        const fileMetadata = {
          name: fileName,
          parents: [targetFolderId],
        };

        const media = {
          mimeType: fileData.mimetype || 'application/octet-stream',
          body: Readable.from(fileData.buffer),
        };

        console.log(`Uploading file: ${fileName} to folder: ${targetFolderId}`);

        const uploadResponse = await this.drive.files.create({
          resource: fileMetadata,
          media: media,
          fields: 'id,webViewLink,webContentLink,name',
        });

        // Make file publicly viewable (skip in mock/dev)
        if (process.env.NODE_ENV !== 'development' || process.env.GOOGLE_SERVICE_ACCOUNT_PATH) {
          try {
            await this.drive.permissions.create({
              fileId: uploadResponse.data.id,
              resource: {
                role: 'reader',
                type: 'anyone',
              },
            });
            console.log(`Set public permissions for file: ${uploadResponse.data.id}`);
          } catch (error) {
            console.warn('Failed to set file permissions:', error.message);
          }
        }

        return {
          fileId: uploadResponse.data.id,
          webViewLink: uploadResponse.data.webViewLink || `https://drive.google.com/file/d/${uploadResponse.data.id}/view`,
          webContentLink: uploadResponse.data.webContentLink || `https://drive.google.com/uc?id=${uploadResponse.data.id}`,
          fileName: uploadResponse.data.name || fileName,
        };
      } catch (error) {
        console.error('Upload error:', error);
        throw new Error(`Upload failed: ${error.message}`);
      }
    });
  }
}

// Export singleton instance
export const googleDriveService = new GoogleDriveService();
