/**
 * Logging utility for the application
 */

import { ENV } from '@/config/environment';

// Log levels
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

const LEVEL_NAMES = {
  0: 'ERROR',
  1: 'WARN',
  2: 'INFO',
  3: 'DEBUG',
};

const LEVEL_COLORS = {
  ERROR: '\x1b[31m', // Red
  WARN: '\x1b[33m',  // Yellow
  INFO: '\x1b[36m',  // Cyan
  DEBUG: '\x1b[37m', // White
};

const RESET_COLOR = '\x1b[0m';

class Logger {
  constructor() {
    this.level = this.getLogLevel();
    this.enableColors = process.env.NODE_ENV === 'development';
  }

  getLogLevel() {
    const level = ENV.LOG_LEVEL?.toLowerCase() || 'info';
    switch (level) {
      case 'error': return LOG_LEVELS.ERROR;
      case 'warn': return LOG_LEVELS.WARN;
      case 'info': return LOG_LEVELS.INFO;
      case 'debug': return LOG_LEVELS.DEBUG;
      default: return LOG_LEVELS.INFO;
    }
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const levelName = LEVEL_NAMES[level];
    
    let formattedMessage = `[${timestamp}] [${levelName}] ${message}`;
    
    if (Object.keys(meta).length > 0) {
      formattedMessage += ` ${JSON.stringify(meta)}`;
    }

    if (this.enableColors) {
      const color = LEVEL_COLORS[levelName] || '';
      formattedMessage = `${color}${formattedMessage}${RESET_COLOR}`;
    }

    return formattedMessage;
  }

  log(level, message, meta = {}) {
    if (level <= this.level) {
      const formattedMessage = this.formatMessage(level, message, meta);
      
      if (level === LOG_LEVELS.ERROR) {
        console.error(formattedMessage);
      } else if (level === LOG_LEVELS.WARN) {
        console.warn(formattedMessage);
      } else {
        console.log(formattedMessage);
      }

      // In production, you might want to send logs to external service
      if (ENV.IS_PROD) {
        this.sendToExternalService(level, message, meta);
      }
    }
  }

  error(message, meta = {}) {
    this.log(LOG_LEVELS.ERROR, message, meta);
  }

  warn(message, meta = {}) {
    this.log(LOG_LEVELS.WARN, message, meta);
  }

  info(message, meta = {}) {
    this.log(LOG_LEVELS.INFO, message, meta);
  }

  debug(message, meta = {}) {
    this.log(LOG_LEVELS.DEBUG, message, meta);
  }

  // Log API requests
  logRequest(req, res, duration) {
    const meta = {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.headers['user-agent'],
      ip: req.headers['x-forwarded-for'] || req.connection?.remoteAddress,
    };

    if (res.statusCode >= 400) {
      this.error(`HTTP ${res.statusCode} ${req.method} ${req.url}`, meta);
    } else {
      this.info(`HTTP ${res.statusCode} ${req.method} ${req.url}`, meta);
    }
  }

  // Log database operations
  logDatabase(operation, collection, query = {}, duration = null) {
    const meta = {
      operation,
      collection,
      query: JSON.stringify(query),
    };

    if (duration) {
      meta.duration = `${duration}ms`;
    }

    this.debug(`Database ${operation} on ${collection}`, meta);
  }

  // Log Google Drive operations
  logGoogleDrive(operation, details = {}) {
    this.info(`Google Drive: ${operation}`, details);
  }

  // Log authentication events
  logAuth(event, user, details = {}) {
    const meta = {
      event,
      user: typeof user === 'string' ? user : user?.email || user?.teamID || 'unknown',
      ...details,
    };

    this.info(`Auth: ${event}`, meta);
  }

  // Log file operations
  logFile(operation, fileName, size = null, type = null) {
    const meta = {
      operation,
      fileName,
    };

    if (size) meta.size = size;
    if (type) meta.type = type;

    this.info(`File: ${operation}`, meta);
  }

  // Send logs to external service (placeholder)
  sendToExternalService(level, message, meta) {
    // Implement external logging service integration here
    // Examples: Winston with external transports, Sentry, LogRocket, etc.
  }
}

// Create singleton instance
const logger = new Logger();

// Request logging middleware
export function requestLogger(req, res, next) {
  if (!ENV.ENABLE_REQUEST_LOGGING) {
    return next();
  }

  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.logRequest(req, res, duration);
  });

  next();
}

// Error logging middleware
export function errorLogger(error, req, res, next) {
  const meta = {
    method: req.method,
    url: req.url,
    stack: error.stack,
    userAgent: req.headers['user-agent'],
  };

  logger.error(`Unhandled error: ${error.message}`, meta);
  next(error);
}

export default logger;
