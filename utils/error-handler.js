/**
 * Error handling utilities
 */

import { NextResponse } from 'next/server';
import { createApiResponse, createErrorResponse } from '@/lib/db-utils';
import logger from './logger';
import { ENV } from '@/config/environment';

// Custom error classes
export class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = null) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message = 'Resource already exists') {
    super(message, 409, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR', { service });
    this.name = 'ExternalServiceError';
  }
}

// Error handler for API routes
export function handleApiError(error, req = null) {
  // Log the error
  const meta = {
    error: error.message,
    stack: error.stack,
    code: error.code,
    statusCode: error.statusCode,
  };

  if (req) {
    meta.method = req.method;
    meta.url = req.url;
  }

  if (error.isOperational) {
    logger.warn(`Operational error: ${error.message}`, meta);
  } else {
    logger.error(`System error: ${error.message}`, meta);
  }

  // Determine response based on error type
  if (error instanceof AppError) {
    return NextResponse.json(
      createErrorResponse(error.message, error.code, error.details),
      { status: error.statusCode }
    );
  }

  // Handle specific error types
  if (error.name === 'ValidationError') {
    return NextResponse.json(
      createErrorResponse('Validation failed', 'VALIDATION_ERROR', error.details),
      { status: 400 }
    );
  }

  if (error.name === 'CastError') {
    return NextResponse.json(
      createErrorResponse('Invalid ID format', 'INVALID_ID'),
      { status: 400 }
    );
  }

  if (error.code === 11000) {
    // MongoDB duplicate key error
    const field = Object.keys(error.keyPattern)[0];
    return NextResponse.json(
      createErrorResponse(`${field} already exists`, 'DUPLICATE_ENTRY'),
      { status: 409 }
    );
  }

  // Default error response
  const message = ENV.IS_PROD ? 'Internal server error' : error.message;
  const details = ENV.IS_PROD ? null : { stack: error.stack };

  return NextResponse.json(
    createErrorResponse(message, 'INTERNAL_ERROR', details),
    { status: 500 }
  );
}

// Async error wrapper for API routes
export function asyncHandler(fn) {
  return async (req, context) => {
    try {
      return await fn(req, context);
    } catch (error) {
      return handleApiError(error, req);
    }
  };
}

// Error boundary for React components
export class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    logger.error('React error boundary caught error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>Please refresh the page or contact support if the problem persists.</p>
          {!ENV.IS_PROD && (
            <details>
              <summary>Error details</summary>
              <pre>{this.state.error?.stack}</pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

// Validation helpers
export function validateRequired(obj, fields) {
  const missing = fields.filter(field => 
    obj[field] === undefined || obj[field] === null || obj[field] === ''
  );

  if (missing.length > 0) {
    throw new ValidationError(`Missing required fields: ${missing.join(', ')}`, { missing });
  }
}

export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('Invalid email format');
  }
}

export function validateFileSize(size, maxSize = ENV.MAX_FILE_SIZE) {
  if (size > maxSize) {
    throw new ValidationError(`File size exceeds maximum allowed size of ${maxSize} bytes`);
  }
}

export function validateFileType(fileName, allowedTypes) {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  if (!allowedTypes.includes(extension)) {
    throw new ValidationError(`File type ${extension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }
}

// Database error handlers
export function handleMongoError(error) {
  if (error.code === 11000) {
    const field = Object.keys(error.keyPattern)[0];
    throw new ConflictError(`${field} already exists`);
  }

  if (error.name === 'ValidationError') {
    const messages = Object.values(error.errors).map(err => err.message);
    throw new ValidationError(`Validation failed: ${messages.join(', ')}`);
  }

  if (error.name === 'CastError') {
    throw new ValidationError('Invalid ID format');
  }

  throw new AppError('Database operation failed', 500, 'DATABASE_ERROR');
}

// Google Drive error handlers
export function handleGoogleDriveError(error) {
  if (error.code === 403) {
    throw new ExternalServiceError('Google Drive', 'Permission denied');
  }

  if (error.code === 404) {
    throw new ExternalServiceError('Google Drive', 'File or folder not found');
  }

  if (error.code === 429) {
    throw new RateLimitError('Google Drive API rate limit exceeded');
  }

  throw new ExternalServiceError('Google Drive', error.message);
}

// JWT error handlers
export function handleJWTError(error) {
  if (error.name === 'TokenExpiredError') {
    throw new AuthenticationError('Token has expired');
  }

  if (error.name === 'JsonWebTokenError') {
    throw new AuthenticationError('Invalid token');
  }

  throw new AuthenticationError('Authentication failed');
}
