/**
 * File utility functions
 */

import { ENV } from '@/config/environment';

/**
 * Validates file size against maximum allowed size
 * @param {number} fileSize - File size in bytes
 * @returns {boolean} Whether file size is valid
 */
export function validateFileSize(fileSize) {
  return fileSize <= ENV.MAX_FILE_SIZE;
}

/**
 * Gets human readable file size
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export function getReadableFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validates file type based on extension
 * @param {string} fileName - File name
 * @param {string} fileType - Expected file type category
 * @returns {boolean} Whether file type is allowed
 */
export function validateFileType(fileName, fileType) {
  const allowedTypes = ENV.ALLOWED_FILE_TYPES[fileType];
  if (!allowedTypes) return false;

  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return allowedTypes.includes(extension);
}

/**
 * Gets file extension from filename
 * @param {string} fileName - File name
 * @returns {string} File extension including the dot
 */
export function getFileExtension(fileName) {
  return fileName.substring(fileName.lastIndexOf('.'));
}

/**
 * Gets file name without extension
 * @param {string} fileName - File name
 * @returns {string} File name without extension
 */
export function getFileNameWithoutExtension(fileName) {
  return fileName.substring(0, fileName.lastIndexOf('.'));
}

/**
 * Sanitizes filename for safe storage
 * @param {string} fileName - Original file name
 * @returns {string} Sanitized file name
 */
export function sanitizeFileName(fileName) {
  return fileName
    .replace(/[^a-zA-Z0-9._-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '')
    .substring(0, 100);
}

/**
 * Generates unique filename with timestamp
 * @param {string} originalName - Original file name
 * @param {string} prefix - Optional prefix
 * @returns {string} Unique file name
 */
export function generateUniqueFileName(originalName, prefix = '') {
  const timestamp = Date.now();
  const extension = getFileExtension(originalName);
  const baseName = sanitizeFileName(getFileNameWithoutExtension(originalName));
  
  return `${prefix}${prefix ? '_' : ''}${timestamp}_${baseName}${extension}`;
}

/**
 * Validates file data object
 * @param {object} fileData - File data object
 * @returns {object} Validation result
 */
export function validateFileData(fileData) {
  const errors = [];

  if (!fileData) {
    errors.push('File data is required');
  } else {
    if (!fileData.buffer) {
      errors.push('File buffer is required');
    }
    if (!fileData.originalname) {
      errors.push('Original filename is required');
    }
    if (!fileData.mimetype) {
      errors.push('File mimetype is required');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets MIME type from file extension
 * @param {string} fileName - File name
 * @returns {string} MIME type
 */
export function getMimeTypeFromExtension(fileName) {
  const extension = getFileExtension(fileName).toLowerCase();
  
  const mimeTypes = {
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.zip': 'application/zip',
    '.rar': 'application/x-rar-compressed',
    '.txt': 'text/plain',
    '.csv': 'text/csv',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.xls': 'application/vnd.ms-excel'
  };

  return mimeTypes[extension] || 'application/octet-stream';
}

/**
 * Checks if file is an image
 * @param {string} fileName - File name
 * @returns {boolean} Whether file is an image
 */
export function isImageFile(fileName) {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  const extension = getFileExtension(fileName).toLowerCase();
  return imageExtensions.includes(extension);
}

/**
 * Checks if file is a document
 * @param {string} fileName - File name
 * @returns {boolean} Whether file is a document
 */
export function isDocumentFile(fileName) {
  const documentExtensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf'];
  const extension = getFileExtension(fileName).toLowerCase();
  return documentExtensions.includes(extension);
}

/**
 * Checks if file is an archive
 * @param {string} fileName - File name
 * @returns {boolean} Whether file is an archive
 */
export function isArchiveFile(fileName) {
  const archiveExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz'];
  const extension = getFileExtension(fileName).toLowerCase();
  return archiveExtensions.includes(extension);
}

/**
 * Creates file metadata object
 * @param {object} fileData - File data
 * @param {string} fileType - File type category
 * @param {string} uploadedBy - User who uploaded the file
 * @returns {object} File metadata
 */
export function createFileMetadata(fileData, fileType, uploadedBy) {
  return {
    originalName: fileData.originalname,
    fileName: generateUniqueFileName(fileData.originalname, fileType),
    fileSize: fileData.buffer?.length || 0,
    mimeType: fileData.mimetype,
    fileType,
    uploadedBy,
    uploadedAt: new Date(),
    isImage: isImageFile(fileData.originalname),
    isDocument: isDocumentFile(fileData.originalname),
    isArchive: isArchiveFile(fileData.originalname)
  };
}

/**
 * Validates multiple files
 * @param {object[]} files - Array of file data objects
 * @param {string} fileType - Expected file type category
 * @returns {object} Validation result
 */
export function validateMultipleFiles(files, fileType) {
  const results = files.map((file, index) => {
    const fileValidation = validateFileData(file);
    const sizeValid = validateFileSize(file.buffer?.length || 0);
    const typeValid = validateFileType(file.originalname, fileType);

    return {
      index,
      fileName: file.originalname,
      isValid: fileValidation.isValid && sizeValid && typeValid,
      errors: [
        ...fileValidation.errors,
        ...(sizeValid ? [] : ['File size exceeds maximum allowed size']),
        ...(typeValid ? [] : ['File type not allowed for this category'])
      ]
    };
  });

  const validFiles = results.filter(r => r.isValid);
  const invalidFiles = results.filter(r => !r.isValid);

  return {
    allValid: invalidFiles.length === 0,
    validCount: validFiles.length,
    invalidCount: invalidFiles.length,
    results,
    validFiles,
    invalidFiles
  };
}
