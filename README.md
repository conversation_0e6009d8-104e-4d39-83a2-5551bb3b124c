# PBL Dashboard

A comprehensive Project-Based Learning (PBL) dashboard for managing IBM SkillsBuild teams, submissions, and Google Drive integration.

## 🌟 Features

- **Team Management**: Register and manage teams with multiple members
- **File Submissions**: Upload certificates, resumes, concept notes, and final deliverables
- **Google Drive Integration**: Automatic folder structure creation and file organization
- **Admin Panel**: Comprehensive admin interface for team and submission management
- **Secure Authentication**: JWT-based authentication for teams and admins
- **Responsive Design**: Modern UI built with Next.js and Tailwind CSS

## 🏗️ Project Structure

```
├── app/                    # Next.js app directory
│   ├── admin/             # Admin pages
│   ├── api/               # API routes
│   └── team/              # Team pages
├── components/            # React components
├── config/               # Configuration files
├── lib/                  # Utility libraries
├── models/               # Database models
├── services/             # Business logic services
├── types/                # TypeScript type definitions
├── utils/                # Utility functions
└── scripts/              # Setup and utility scripts
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- MongoDB (local or Atlas)
- Google Cloud Project with Drive API enabled
- Google Service Account credentials

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd PBL_Dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the setup script**
   ```bash
   node scripts/setup.js
   ```
   This interactive script will help you configure the environment.

4. **Manual setup (alternative)**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## ⚙️ Configuration

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# Application
NODE_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000
PORT=3000

# Database
MONGODB_URI=mongodb://localhost:27017/ibm-skillsbuild

# Google Drive API
GOOGLE_SERVICE_ACCOUNT_PATH=./google-credentials.json
ADMIN_EMAIL=<EMAIL>

# Security
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# File Upload
MAX_FILE_SIZE=********
```

### Google Drive Setup

1. **Create a Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one

2. **Enable Google Drive API**
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google Drive API" and enable it

3. **Create Service Account**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "Service Account"
   - Download the JSON key file
   - Save it as `google-credentials.json` in the project root

4. **Configure Permissions**
   - The application will automatically create a "TestDrive" folder
   - This folder will be shared with everyone (anyone with the link)
   - Team folders will be created under TestDrive/IBM_SkillsBuild_Teams/

## 📁 Folder Structure

The application creates the following Google Drive structure:

```
TestDrive/                          # Main shared folder
└── IBM_SkillsBuild_Teams/         # Teams container
    └── [TEAM_ID]_[TEAM_NAME]/     # Individual team folder
        ├── Concept_Note/          # Concept note submissions
        │   ├── Problem_Statement/
        │   ├── Solution_Approach/
        │   ├── Technical_Architecture/
        │   ├── Implementation_Plan/
        │   └── Team_Roles/
        ├── Final_Deliverable/     # Final project submissions
        └── Member_Submissions/    # Individual member files
            └── Member_[N]_[NAME]/ # Per-member folders
                ├── Certificates/
                └── Resume_LinkedIn/
```

## 🔐 Authentication

### Team Login
- Teams log in using their Team ID and email
- JWT tokens are issued for authenticated sessions

### Admin Login
- Admins log in using username and password
- Separate admin authentication system

## 📡 API Endpoints

### Team APIs
- `POST /api/team/login` - Team authentication
- `POST /api/team/upload` - File upload
- `GET /api/team/[teamID]` - Get team details

### Admin APIs
- `POST /api/admin/login` - Admin authentication
- `GET /api/admin/teams` - List all teams
- `POST /api/admin/folders` - Manage folder structures
- `POST /api/admin/teams/upload` - Bulk team upload

## 🛠️ Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Code Structure

- **Services**: Business logic separated into service classes
- **Utils**: Utility functions for common operations
- **Types**: TypeScript definitions for type safety
- **Config**: Centralized configuration management

### Adding New Features

1. Create service classes in `services/`
2. Add utility functions in `utils/`
3. Define types in `types/`
4. Create API routes in `app/api/`
5. Build UI components in `components/`

## 🔧 Troubleshooting

### Common Issues

1. **Google Drive API Errors**
   - Ensure service account has proper permissions
   - Check if Google Drive API is enabled
   - Verify credentials file path

2. **MongoDB Connection Issues**
   - Check MongoDB URI format
   - Ensure MongoDB service is running
   - Verify network connectivity

3. **File Upload Issues**
   - Check file size limits
   - Verify file type restrictions
   - Ensure proper folder structure exists

### Debug Mode

Set `NODE_ENV=development` to enable:
- Detailed error messages
- Mock Google Drive client (when credentials unavailable)
- Enhanced logging

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md)
- [Contributing Guidelines](docs/contributing.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the troubleshooting section above
