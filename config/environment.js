/**
 * Environment Configuration
 * Centralized configuration management for the application
 */

/**
 * Validates required environment variables
 * @param {string[]} requiredVars - Array of required environment variable names
 * @throws {Error} If any required variables are missing
 */
function validateRequiredEnvVars(requiredVars) {
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Environment configuration object
 */
export const ENV = {
  // Node environment
  NODE_ENV: process.env.NODE_ENV || 'development',
  IS_PROD: process.env.NODE_ENV === 'production',
  IS_DEV: process.env.NODE_ENV === 'development',

  // Application URLs
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  PORT: parseInt(process.env.PORT || '3000', 10),

  // Database
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/ibm-skillsbuild',

  // Google Drive API
  GOOGLE_SERVICE_ACCOUNT_PATH: process.env.GOOGLE_SERVICE_ACCOUNT_PATH || '',
  ADMIN_EMAIL: process.env.ADMIN_EMAIL || '<EMAIL>',

  // JWT Configuration
  JWT_SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '24h',

  // File Upload Configuration
  MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || '********', 10), // 10MB default
  ALLOWED_FILE_TYPES: {
    certificate: ['.pdf', '.jpg', '.jpeg', '.png'],
    resume: ['.pdf', '.doc', '.docx'],
    conceptNote: ['.pdf', '.doc', '.docx'],
    finalDeliverable: ['.pdf', '.doc', '.docx', '.zip', '.rar'],
  },

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),

  // Logging
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  ENABLE_REQUEST_LOGGING: process.env.ENABLE_REQUEST_LOGGING === 'true',

  // Security
  BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  CORS_ORIGIN: process.env.CORS_ORIGIN || '*',

  // Google Drive Configuration
  DRIVE_CONFIG: {
    MAX_RETRIES: parseInt(process.env.DRIVE_MAX_RETRIES || '3', 10),
    INITIAL_DELAY_MS: parseInt(process.env.DRIVE_INITIAL_DELAY_MS || '1000', 10),
    BACKOFF_FACTOR: parseInt(process.env.DRIVE_BACKOFF_FACTOR || '2', 10),
    TEST_DRIVE_FOLDER_NAME: process.env.TEST_DRIVE_FOLDER_NAME || 'TestDrive',
  },

  // Team Configuration
  TEAM_CONFIG: {
    MIN_MEMBERS: parseInt(process.env.TEAM_MIN_MEMBERS || '1', 10),
    MAX_MEMBERS: parseInt(process.env.TEAM_MAX_MEMBERS || '10', 10),
    TEAM_ID_PREFIX: process.env.TEAM_ID_PREFIX || 'IBMSB2025',
  },

  // Email Configuration (if needed in future)
  EMAIL_CONFIG: {
    SMTP_HOST: process.env.SMTP_HOST || '',
    SMTP_PORT: parseInt(process.env.SMTP_PORT || '587', 10),
    SMTP_USER: process.env.SMTP_USER || '',
    SMTP_PASS: process.env.SMTP_PASS || '',
    FROM_EMAIL: process.env.FROM_EMAIL || '<EMAIL>',
  },
};

/**
 * Validate critical environment variables in production
 */
export function validateEnvironment() {
  if (ENV.IS_PROD) {
    const requiredProdVars = [
      'MONGODB_URI',
      'JWT_SECRET',
      'NEXT_PUBLIC_BASE_URL'
    ];

    try {
      validateRequiredEnvVars(requiredProdVars);
      console.log('✅ All required production environment variables are set');
    } catch (error) {
      console.error('❌ Environment validation failed:', error.message);
      process.exit(1);
    }
  }

  // Validate Google Drive configuration if service account path is provided
  if (ENV.GOOGLE_SERVICE_ACCOUNT_PATH) {
    try {
      const fs = require('fs');
      if (!fs.existsSync(ENV.GOOGLE_SERVICE_ACCOUNT_PATH)) {
        console.warn('⚠️ Google Service Account file not found at specified path');
      } else {
        console.log('✅ Google Service Account file found');
      }
    } catch (error) {
      console.warn('⚠️ Could not validate Google Service Account file:', error.message);
    }
  } else {
    console.log('ℹ️ No Google Service Account path provided, using mock client for development');
  }
}

/**
 * Get database connection string with validation
 */
export function getDatabaseUrl() {
  if (!ENV.MONGODB_URI) {
    throw new Error('MONGODB_URI environment variable is required');
  }
  return ENV.MONGODB_URI;
}

/**
 * Get JWT configuration
 */
export function getJWTConfig() {
  return {
    secret: ENV.JWT_SECRET,
    expiresIn: ENV.JWT_EXPIRES_IN,
  };
}

/**
 * Get file upload configuration
 */
export function getFileUploadConfig() {
  return {
    maxFileSize: ENV.MAX_FILE_SIZE,
    allowedTypes: ENV.ALLOWED_FILE_TYPES,
  };
}

/**
 * Get Google Drive configuration
 */
export function getGoogleDriveConfig() {
  return {
    serviceAccountPath: ENV.GOOGLE_SERVICE_ACCOUNT_PATH,
    adminEmail: ENV.ADMIN_EMAIL,
    ...ENV.DRIVE_CONFIG,
  };
}

/**
 * Initialize environment validation
 */
if (typeof window === 'undefined') {
  // Only run validation on server side
  validateEnvironment();
}

export default ENV;
