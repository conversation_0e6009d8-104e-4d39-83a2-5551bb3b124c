/**
 * TypeScript type definitions for the application
 */

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  timestamp: string;
  data?: T;
}

export interface ErrorResponse {
  success: false;
  message: string;
  error: {
    code: string;
    timestamp: string;
    details?: any;
  };
}

// Team Types
export interface TeamMember {
  fullName: string;
  email: string;
  phone?: string;
  role?: string;
  studentId?: string;
}

export interface Team {
  _id?: string;
  teamID: string;
  teamName: string;
  collegeName: string;
  leaderName: string;
  email: string;
  phone: string;
  members: TeamMember[];
  folderStructure?: FolderStructure;
  folderStructureEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Folder Structure Types
export interface ConceptNoteSubfolders {
  Problem_Statement: string;
  Solution_Approach: string;
  Technical_Architecture: string;
  Implementation_Plan: string;
  Team_Roles: string;
}

export interface MemberFolder {
  folderId: string;
  certificateFolderId: string;
  resumeFolderId: string;
  memberName: string;
  memberEmail: string;
}

export interface FolderStructure {
  testDriveFolderId: string;
  teamsFolderId: string;
  teamFolderId: string;
  teamFolderLink: string;
  conceptNoteFolderId: string;
  conceptNoteSubfolders: ConceptNoteSubfolders;
  finalDeliverableFolderId: string;
  membersSubmissionsFolderId: string;
  memberFolders: { [key: number]: MemberFolder };
  createdAt: Date;
  customFolderNames: FolderNames;
}

export interface FolderNames {
  rootFolder: string;
  conceptNoteFolder: string;
  finalDeliverableFolder: string;
  memberSubmissionsFolder: string;
  certificatesFolder: string;
  resumeFolder: string;
  conceptNoteSubcategories: {
    problemStatement: string;
    solutionApproach: string;
    technicalArchitecture: string;
    implementationPlan: string;
    teamRoles: string;
  };
}

// File Upload Types
export interface FileData {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size?: number;
}

export interface UploadResult {
  fileId: string;
  webViewLink: string;
  webContentLink: string;
  fileName: string;
  folderPath?: string;
}

export type FileType = 'certificate' | 'resume' | 'conceptNote' | 'finalDeliverable';
export type ConceptNoteSubCategory = 'Problem_Statement' | 'Solution_Approach' | 'Technical_Architecture' | 'Implementation_Plan' | 'Team_Roles';

// Google Drive Types
export interface DrivePermission {
  role: 'reader' | 'writer' | 'owner';
  type: 'user' | 'anyone' | 'domain';
  emailAddress?: string;
}

export interface DriveFile {
  id: string;
  name: string;
  webViewLink?: string;
  webContentLink?: string;
  mimeType?: string;
  parents?: string[];
}

// Admin Types
export interface Admin {
  _id?: string;
  username: string;
  email: string;
  password: string;
  role: 'admin' | 'super_admin';
  createdAt: Date;
  updatedAt: Date;
}

// Notice Types
export interface Notice {
  _id?: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error';
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Global Settings Types
export interface GlobalSettings {
  _id?: string;
  settingType: 'folderStructure' | 'general';
  folderStructure?: FolderNames;
  generalSettings?: {
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Authentication Types
export interface LoginCredentials {
  teamID?: string;
  email?: string;
  username?: string;
  password?: string;
}

export interface AuthToken {
  token: string;
  expiresIn: string;
  user: {
    id: string;
    email: string;
    role?: string;
    teamID?: string;
  };
}

// Form Types
export interface TeamRegistrationForm {
  teamName: string;
  collegeName: string;
  leaderName: string;
  email: string;
  phone: string;
  members: TeamMember[];
}

// Validation Types
export interface ValidationResult {
  success: boolean;
  missing: string[];
  message: string;
}

// Environment Types
export interface EnvironmentConfig {
  NODE_ENV: string;
  IS_PROD: boolean;
  IS_DEV: boolean;
  BASE_URL: string;
  PORT: number;
  MONGODB_URI: string;
  GOOGLE_SERVICE_ACCOUNT_PATH: string;
  ADMIN_EMAIL: string;
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  MAX_FILE_SIZE: number;
}

// Component Props Types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  className?: string;
}

// Table Types
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
}

export interface TableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    onPageChange: (page: number) => void;
  };
}

// Utility Types
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

// Export all types as a namespace for easier importing
export namespace Types {
  export type ApiResponse<T = any> = ApiResponse<T>;
  export type Team = Team;
  export type TeamMember = TeamMember;
  export type FolderStructure = FolderStructure;
  export type FileData = FileData;
  export type UploadResult = UploadResult;
  export type FileType = FileType;
  export type Admin = Admin;
  export type Notice = Notice;
  export type GlobalSettings = GlobalSettings;
}
